{"version": "2.0.0", "tasks": [{"label": "Start All Development Servers", "type": "shell", "command": "bun", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "<PERSON> (Legacy)", "type": "shell", "command": "bun run apps/studio/dev.ts", "group": "build"}, {"label": "Start API Server", "type": "shell", "command": "bun", "args": ["run", "dev:api"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start UI Development", "type": "shell", "command": "bun", "args": ["run", "dev:ui"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Electron", "type": "shell", "command": "bun", "args": ["run", "dev:electron"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}