{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.insertSpaces": true, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.next": true, "**/out": true, "**/*.log": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.next/**": true, "**/out/**": true}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "eslint.enable": true, "eslint.workingDirectories": ["apps/studio", "apps/studio/ui", "apps/api-server", "packages"], "typescript.preferences.importModuleSpecifier": "relative", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.tsx": "${capture}.js", "package.json": "package-lock.json,yarn.lock,bun.lockb", "tsconfig.json": "tsconfig.*.json", ".env": ".env.*"}}